import { Document, Schema, Types, model } from 'mongoose';

export interface UserMongoI extends Document {
  email: string;
  password: string;
  name: string;
  image: Types.ObjectId;
  city: string;
  isVerified: boolean;
  role: string;
  settings: {
    allowedRegions: string[];
  };
  googleSignfailureCount: number;
  googleRefreshToken: string;
  homeVisitor: boolean;
  homeVisitorColor: string;
}

export enum Roles {
  superadmin = 'superadmin',
  administrador = 'administrador',
  agent = 'agent',
  sales = 'sales',
  'fleet-manager' = 'fleet-manager',
  lead = 'lead',
  auditor = 'auditor',
  collection = 'collection',
}

const userSchema = new Schema({
  email: {
    type: String,
    unique: true,
    required: [true, 'Email is required'],
  },
  password: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  image: {
    type: Schema.Types.ObjectId, // Tipo de datos ObjectId para la referencia al modelo User
    ref: 'Document',
  },
  city: {
    type: String,
    enum: [
      'cdmx',
      'gdl',
      'mty',
      'tij',
      'pbc',
      'pue',
      'pbe',
      'qro',
      'pbe',
      'tol',
      'ptv',
      'tep',
      'col',
      'sal',
      'torr',
      'dur',
      'mxli',
      'her',
      'chi',
      'leo',
      'ags',
      'slp',
      'mer',
    ],
    required: true,
  },
  isVerified: { type: Boolean, default: false },
  role: {
    type: String,
    enum: Object.values(Roles),
    required: [true, 'Role is required'],
  },
  settings: {
    type: {
      allowedRegions: {
        type: [
          {
            type: String,
            enum: [
              'cdmx',
              'gdl',
              'mty',
              'tij',
              'pbc',
              'pbe',
              'pue',
              'qro',
              'moka',
              'all',
              'pbe',
              'tol',
              'ptv',
              'tep',
              'col',
              'sal',
              'torr',
              'dur',
              'mxli',
              'her',
              'chi',
              'leo',
              'ags',
              'slp',
              'mer',
            ],
            required: true,
          },
        ],
        required: true,
      },
    },
    required: true,
  },
  googleSignfailureCount: { type: Number, default: 0 },
  googleRefreshToken: { type: String, required: false },
  homeVisitor: { type: Boolean, default: false },
  homeVisitorColor: { type: String, required: false },
});

const User = model('User', userSchema);

export const UserMongo = User;

export default User;
