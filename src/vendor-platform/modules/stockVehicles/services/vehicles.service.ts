import Service from '@vendor/modules/services/models/service.model';
import StockVehicle from '@/models/StockVehicleSchema';
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';

export class StockVehicleService {
  async getByQuery(q: string, organizationId: string) {
    const stockVehicles = await StockVehicle.find({
      $or: [
        { brand: { $regex: q, $options: 'i' } },
        { model: { $regex: q, $options: 'i' } },
        { carNumber: { $regex: q, $options: 'i' } },
        { vin: { $regex: q, $options: 'i' } },
        { 'carPlates.plates': { $regex: q, $options: 'i' } },
      ],
    })
      .select({
        brand: 1,
        model: 1,
        carNumber: 1,
        extensionCarNumber: 1,
        color: 1,
        vin: 1,
        carPlates: {
          plates: 1,
        },
        stock: 1,
        drivers: 1,
      })
      .populate({
        path: 'drivers',
        select: '_id firstName lastName email phone curp rfc',
      });

    // if (!stockVehicles.length) return [];

    // if there is no stock vehicles, return an array of vehicles that have a service pending
    if (!stockVehicles.length) {
      const services = await Service.find({
        organizationId: organizationId,
        status: 'pending',
      }).select({
        stockId: 1,
        status: 1,
      });

      const stockIds = services.map((service) => service.stockId.toString());

      const vehicles = await StockVehicle.find({
        _id: { $in: stockIds },
      })
        .select({
          brand: 1,
          model: 1,
          carNumber: 1,
          extensionCarNumber: 1,
          color: 1,
          vin: 1,
          carPlates: {
            plates: 1,
          },
          stock: 1,
          drivers: 1,
        })
        .populate({
          path: 'drivers',
          select: '_id firstName lastName email phone curp rfc',
        })
        .lean();

      const vehiclesMap = vehicles.reduce(
        (map, vehicle) => {
          map[vehicle._id.toString()] = {
            ...vehicle,
            contractNumber: vehicle.extensionCarNumber
              ? `${vehicle.carNumber}-${vehicle.extensionCarNumber}`
              : vehicle.carNumber,
          };
          return map;
        },
        {} as Record<string, any>
      );

      const vehiclesWithService = services.map((service) => {
        return {
          ...vehiclesMap[service.stockId] /* .toObject() */,
          service: service,
        };
      });

      return vehiclesWithService;
    }

    const stockIds = stockVehicles.map((vehicle) => vehicle._id.toString());

    const services = await Service.find({
      stockId: { $in: stockIds },
      organizationId: organizationId,
      status: 'pending',
    }).select({
      stockId: 1,
      status: 1,
    });

    const servicesMap = services.reduce(
      (map, service) => {
        map[service.stockId] = service;
        return map;
      },
      {} as Record<string, any>
    );

    const enrichedVehicles = stockVehicles.map((vehicle) => ({
      ...vehicle.toObject(),
      contractNumber: vehicle.extensionCarNumber
        ? `${vehicle.carNumber}-${vehicle.extensionCarNumber}`
        : vehicle.carNumber,
      service: servicesMap[vehicle._id.toString()] || null,
    }));

    return enrichedVehicles;
  }

  async getById(id: string) {
    const stockVehicle = await StockVehicle.findById(id).select({
      brand: 1,
      model: 1,
      carNumber: 1,
      extensionCarNumber: 1,
      color: 1,
      km: 1,
      vin: 1,
      carPlates: {
        plates: 1,
      },
      stock: 1,
      createdAt: 1,
      updatedAt: 1,
    });

    if (!stockVehicle) return HttpException.NotFound('Vehicle not found');

    // return stockVehicle;

    return {
      ...stockVehicle.toObject(),
      contractNumber: stockVehicle.extensionCarNumber
        ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
        : stockVehicle.carNumber,
    };
  }
}

export const stockVehicleService = new StockVehicleService();
